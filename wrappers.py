from pathlib import Path

import numpy as np
from max.torch import CustomOpLibrary

import torch

# TODO: one file for wrappers one for tests


# def get_launch_config(input_size: int) -> tuple[int, int]:
#     props = torch.cuda.get_device_properties()
#     warp_size = props.warp_size
#     MAX_TPB = 1024
#     optimal_tpb = min(MAX_TPB, ((input_size + warp_size - 1) // warp_size) * warp_size)
#     optimal_tpb = 8
#     blocks_needed = (input_size + optimal_tpb - 1) // optimal_tpb
#     return blocks_needed, optimal_tpb


def prefix_sum_multiblock_pytorch(input_tensor: torch.Tensor) -> torch.Tensor:
    """
    Wrapper function for prefix_sum_multiblock mojo kernel.
    """
    # Load our custom operations
    mojo_kernels = Path(__file__).parent / "op"
    ops = CustomOpLibrary(mojo_kernels)

    # Output tensor needs space for block sums
    tpb = 8
    blocks_needed = (input_tensor.shape[0] + tpb - 1) // tpb
    output_shape = list(input_tensor.shape)
    output_shape[0] += blocks_needed
    print(f"output_shape: {output_shape}")
    # new_empty copies the dtype and device from the input tensor
    # both input and output tensors are created on the GPU
    output_tensor = input_tensor.new_empty(output_shape)

    # Call our custom prefix_sum_multiblock operation with explicit output tensor
    # The Mojo signature expects: (out, input)
    # "prefix_sum_multiblock" matches the @compiler.register("prefix_sum_multiblock") in op/prefix_sum_multiblock.mojo
    prefix_sum_multiblock = ops.prefix_sum_multiblock
    torch.compile(prefix_sum_multiblock)(output_tensor, input_tensor)

    return output_tensor


def prefix_sum_simple_pytorch(input_tensor: torch.Tensor) -> torch.Tensor:
    """
    Wrapper function for prefix_sum_simple mojo kernel.
    """
    # Load our custom operations
    mojo_kernels = Path(__file__).parent / "op"
    ops = CustomOpLibrary(mojo_kernels)

    # Create output tensor with same shape as input
    output_tensor = torch.empty_like(input_tensor)

    # Call our custom conv1d operation with explicit output tensor
    # The Mojo signature expects: (out, input)
    # "prefix_sum_simple" matches the @compiler.register("prefix_sum_simple") in op/prefix_sum_simple.mojo
    prefix_sum_simple = ops.prefix_sum_simple[{"size": input_tensor.shape[0]}]
    torch.compile(prefix_sum_simple)(output_tensor, input_tensor)

    return output_tensor


# TODO: Maybe compare against https://github.com/glassroom/torch_parallel_scan ?
# def compute_numpy_reference(
#     input_array: np.ndarray, kernel_array: np.ndarray
# ) -> np.ndarray:
#     """NumPy reference implementation for verification."""
#     INPUT_SIZE = len(input_array)
#     KERNEL_SIZE = len(kernel_array)

#     expected_result = np.zeros_like(input_array, dtype=np.float32)
#     for i in range(INPUT_SIZE):
#         for j in range(KERNEL_SIZE):
#             if i + j < INPUT_SIZE:
#                 expected_result[i] += input_array[i + j] * kernel_array[j]
#     return expected_result
