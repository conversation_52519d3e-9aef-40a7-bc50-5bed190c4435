{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "mojo-cuda-gdb",
            "request": "launch",
            "name": "Mojo CUDA-GDB Debug",
            "description": "Launch and debug the Mojo file that is active on the editor when the debug session starts, using CUDA-GDB.",
            "mojoFile": "${file}",
            "args": [],
            "env": [],
            "cwd": "${workspaceFolder}",
            "breakOnLaunch": true,
            "program": "${workspaceFolder}/op/prefix_sum_multiblock_debug",
            // "legacyDebugger": true,
            // "initCommands": [
            //     "set environment CUDBG_USE_LEGACY_DEBUGGER=1"
            // ],
        },
        {
            "type": "mojo-lldb",
            "request": "launch",
            "name": "Mojo: Debug current Mojo file",
            "description": "Launch and debug the Mojo file that is active on the editor when the debug session starts.",
            "mojoFile": "${file}",
            "args": [],
            "env": [],
            "cwd": "${workspaceFolder}",
            "runInTerminal": false
        },
        {
            "type": "mojo-lldb",
            "request": "launch",
            "name": "Mojo: Debug Mojo file",
            "description": "Launch and debug a Mojo file given its path.",
            "mojoFile": "${workspaceFolder}/<your_file.mojo>",
            "args": [],
            "env": [],
            "cwd": "${workspaceFolder}",
            "runInTerminal": false
        },
        {
            "type": "mojo-lldb",
            "request": "launch",
            "name": "Mojo: Debug binary",
            "description": "Launch and debug a precompiled binary given its path.",
            "program": "${workspaceFolder}/<your_binary>",
            "args": [],
            "env": [],
            "cwd": "${workspaceFolder}",
            "runInTerminal": false
        },
        {
            "type": "mojo-lldb",
            "request": "attach",
            "name": "Mojo: Attach to process",
            "description": "Search for a process to attach the debugger to.",
            "pid": "${command:pickProcessToAttach}"
        },
        {
            "type": "mojo-lldb",
            "request": "launch",
            "name": "Mojo: Debug prefix_sum_multiblock.mojo ⸱ pscan_torch/op/prefix_sum_multiblock.mojo",
            "mojoFile": "/home/<USER>/pscan_torch/op/prefix_sum_multiblock.mojo",
            "args": [],
            "env": [],
            "runInTerminal": false
        }
    ]
}