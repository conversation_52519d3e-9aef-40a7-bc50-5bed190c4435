version: 6
environments:
  cuda:
    channels:
    - url: https://conda.anaconda.org/nvidia/
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bash-5.2.37-h4be8908_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.44-h4bf12b8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_linux-64-2.44-h4852527_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cccl_linux-64-12.9.27-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-command-line-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-compiler-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-crt-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-crt-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-dev_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-static-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-static_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuobjdump-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuxxfilt-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-driver-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-driver-dev_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-gdb-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-dev-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nsight-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvcc-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-impl-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvdisasm-12.9.88-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvml-dev-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprof-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprune-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-dev-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvtx-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvvm-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-impl-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvp-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-12.9.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-dev-12.9.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-profiler-api-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-sanitizer-api-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-toolkit-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-version-12.9-3.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-visual-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.4.0-h69c5793_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_linux-64-13.4.0-h621f4e2_11.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/gds-tools-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.4.0-haf17267_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_linux-64-13.4.0-he431e45_11.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-34_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-34_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-12.9.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-dev-12.9.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-11.4.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-dev-11.4.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-dev-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-10.3.10.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-dev-10.3.10.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-11.7.5.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-dev-11.7.5.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-12.5.10.65-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-dev-12.5.10.65-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.4.0-hba01cd7_104.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.3-h1fed272_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h3b78370_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-34_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-12.4.1.87-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-dev-12.4.1.87-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-12.9.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-dev-12.9.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-dev-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-12.4.0.76-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-dev-12.4.0.76-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h421ea60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.4.0-h14bf0c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.4.0-hba01cd7_104.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.11.0-he8b52b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h2cb61b6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-25.6.0.dev2025090405-3.12release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.6.0.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-compiler-********.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-python-********.dev2025090405-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/nsight-compute-2025.2.1.3-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nspr-4.37-h29cc59b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nss-3.115-hc3c8bcf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.46-h1321c63_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.0-hab00c5b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-gpl-tools-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-tools-5.8.1-hb9d3cd8_2.conda
      - pypi: https://files.pythonhosted.org/packages/42/14/42b2651a2f46b022ccd948bca9f2d5af0fd8929c4eec235b8d6d844fbe67/filelock-3.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/71/70db47e4f6ce3e5c37a607355f80da8860a33226be640226ac52cb05ef2e/fsspec-2025.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/62/a1/3d680cbfd5f4b8f15abc1d571870c5fc3e594bb582bc3b64ea099db13e56/jinja2-3.1.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/f0/89e7aadfb3749d0f52234a0c8c7867877876e0a20b60e2188e9850794c17/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/82/df/4b01f10069e23c641f116c62fc31e31e8dc361a153175d81561d15c8143b/nvidia_cublas_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/39/6f/3683ecf4e38931971946777d231c2df00dd5c1c4c2c914c42ad8f9f4dca6/nvidia_cuda_cupti_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d4/22/32029d4583f7b19cfe75c84399cbcfd23f2aaf41c66fc8db4da460104fff/nvidia_cuda_nvrtc_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/16/f6/0e1ef31f4753a44084310ba1a7f0abaf977ccd810a604035abb43421c057/nvidia_cuda_runtime_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/25/dc/dc825c4b1c83b538e207e34f48f86063c88deaa35d46c651c7c181364ba2/nvidia_cudnn_cu12-********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ac/26/b53c493c38dccb1f1a42e1a21dc12cba2a77fbe36c652f7726d9ec4aba28/nvidia_cufft_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e5/9c/1f3264d0a84c8a031487fb7f59780fc78fa6f1c97776233956780e3dc3ac/nvidia_cufile_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/bd/fc/7be5d0082507269bb04ac07cc614c84b78749efb96e8cf4100a8a1178e98/nvidia_curand_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c2/08/953675873a136d96bb12f93b49ba045d1107bc94d2551c52b12fa6c7dec3/nvidia_cusolver_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c2/ab/31e8149c66213b846c082a3b41b1365b831f41191f9f40c6ddbc8a7d550e/nvidia_cusparse_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/3b/9a/72ef35b399b0e183bc2e8f6f558036922d453c4d8237dab26c666a04244b/nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/67/ca/f42388aed0fddd64ade7493dbba36e1f534d4e6fdbdd355c6a90030ae028/nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/03/f8/9d85593582bd99b8d7c65634d2304780aefade049b2b94d96e44084be90b/nvidia_nvjitlink_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8d/cd/0e8c51b2ae3a58f054f2e7fe91b82d201abfb30167f2431e9bd92d532f42/nvidia_nvtx_cu12-12.8.55-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl
      - pypi: https://download.pytorch.org/whl/cu128/torch-2.7.1%2Bcu128-cp312-cp312-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/24/5f/950fb373bf9c01ad4eb5a8cd5eaf32cdf9e238c02f9293557a2129b9c4ac/triton-3.3.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
  default:
    channels:
    - url: https://conda.anaconda.org/nvidia/
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bash-5.2.37-h4be8908_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.44-h4bf12b8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_linux-64-2.44-h4852527_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cccl_linux-64-12.9.27-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-command-line-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-compiler-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-crt-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-crt-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-dev_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-static-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-static_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuobjdump-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuxxfilt-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-driver-dev-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-driver-dev_linux-64-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-gdb-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-dev-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nsight-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvcc-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-impl-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvdisasm-12.9.88-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvml-dev-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprof-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprune-12.9.82-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-dev-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvtx-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvvm-dev_linux-64-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-impl-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-tools-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvp-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-12.9.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-dev-12.9.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-profiler-api-12.9.79-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-sanitizer-api-12.9.79-1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-toolkit-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/nvidia/noarch/cuda-version-12.9-3.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/cuda-visual-tools-12.9.1-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.4.0-h69c5793_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_linux-64-13.4.0-h621f4e2_11.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/gds-tools-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.4.0-haf17267_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_linux-64-13.4.0-he431e45_11.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-34_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-34_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-12.9.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-dev-12.9.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-11.4.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-dev-11.4.1.4-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-dev-1.14.1.1-4.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-10.3.10.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-dev-10.3.10.19-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-11.7.5.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-dev-11.7.5.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-12.5.10.65-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-dev-12.5.10.65-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.4.0-hba01cd7_104.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.3-h1fed272_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h3b78370_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-34_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-12.4.1.87-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-dev-12.4.1.87-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-12.9.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-dev-12.9.82-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-dev-12.9.86-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-12.4.0.76-0.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-dev-12.4.0.76-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h421ea60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.4.0-h14bf0c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.4.0-hba01cd7_104.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.11.0-he8b52b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h2cb61b6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-25.6.0.dev2025090405-3.12release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.6.0.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-compiler-********.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-python-********.dev2025090405-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/nvidia/linux-64/nsight-compute-2025.2.1.3-0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nspr-4.37-h29cc59b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nss-3.115-hc3c8bcf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.46-h1321c63_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.0-hab00c5b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-gpl-tools-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-tools-5.8.1-hb9d3cd8_2.conda
      - pypi: https://files.pythonhosted.org/packages/42/14/42b2651a2f46b022ccd948bca9f2d5af0fd8929c4eec235b8d6d844fbe67/filelock-3.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/71/70db47e4f6ce3e5c37a607355f80da8860a33226be640226ac52cb05ef2e/fsspec-2025.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/62/a1/3d680cbfd5f4b8f15abc1d571870c5fc3e594bb582bc3b64ea099db13e56/jinja2-3.1.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/f0/89e7aadfb3749d0f52234a0c8c7867877876e0a20b60e2188e9850794c17/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/82/df/4b01f10069e23c641f116c62fc31e31e8dc361a153175d81561d15c8143b/nvidia_cublas_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/39/6f/3683ecf4e38931971946777d231c2df00dd5c1c4c2c914c42ad8f9f4dca6/nvidia_cuda_cupti_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d4/22/32029d4583f7b19cfe75c84399cbcfd23f2aaf41c66fc8db4da460104fff/nvidia_cuda_nvrtc_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/16/f6/0e1ef31f4753a44084310ba1a7f0abaf977ccd810a604035abb43421c057/nvidia_cuda_runtime_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/25/dc/dc825c4b1c83b538e207e34f48f86063c88deaa35d46c651c7c181364ba2/nvidia_cudnn_cu12-********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ac/26/b53c493c38dccb1f1a42e1a21dc12cba2a77fbe36c652f7726d9ec4aba28/nvidia_cufft_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e5/9c/1f3264d0a84c8a031487fb7f59780fc78fa6f1c97776233956780e3dc3ac/nvidia_cufile_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/bd/fc/7be5d0082507269bb04ac07cc614c84b78749efb96e8cf4100a8a1178e98/nvidia_curand_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c2/08/953675873a136d96bb12f93b49ba045d1107bc94d2551c52b12fa6c7dec3/nvidia_cusolver_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c2/ab/31e8149c66213b846c082a3b41b1365b831f41191f9f40c6ddbc8a7d550e/nvidia_cusparse_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/3b/9a/72ef35b399b0e183bc2e8f6f558036922d453c4d8237dab26c666a04244b/nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/67/ca/f42388aed0fddd64ade7493dbba36e1f534d4e6fdbdd355c6a90030ae028/nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/03/f8/9d85593582bd99b8d7c65634d2304780aefade049b2b94d96e44084be90b/nvidia_nvjitlink_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8d/cd/0e8c51b2ae3a58f054f2e7fe91b82d201abfb30167f2431e9bd92d532f42/nvidia_nvtx_cu12-12.8.55-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl
      - pypi: https://download.pytorch.org/whl/cu128/torch-2.7.1%2Bcu128-cp312-cp312-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/24/5f/950fb373bf9c01ad4eb5a8cd5eaf32cdf9e238c02f9293557a2129b9c4ac/triton-3.3.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
  rocm:
    channels:
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bash-5.2.37-h4be8908_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-34_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-34_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-34_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-25.6.0.dev2025090405-3.12release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.6.0.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-compiler-********.dev2025090405-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-python-********.dev2025090405-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.0-hab00c5b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-gpl-tools-5.8.1-hbcc6ac9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-tools-5.8.1-hb9d3cd8_2.conda
      - pypi: https://files.pythonhosted.org/packages/42/14/42b2651a2f46b022ccd948bca9f2d5af0fd8929c4eec235b8d6d844fbe67/filelock-3.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/71/70db47e4f6ce3e5c37a607355f80da8860a33226be640226ac52cb05ef2e/fsspec-2025.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/62/a1/3d680cbfd5f4b8f15abc1d571870c5fc3e594bb582bc3b64ea099db13e56/jinja2-3.1.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/f0/89e7aadfb3749d0f52234a0c8c7867877876e0a20b60e2188e9850794c17/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl
      - pypi: https://download.pytorch.org/whl/pytorch_triton_rocm-3.3.1-cp312-cp312-linux_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl
      - pypi: https://download.pytorch.org/whl/rocm6.3/torch-2.7.1%2Brocm6.3-cp312-cp312-manylinux_2_28_x86_64.whl
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/linux-64/bash-5.2.37-h4be8908_0.conda
  sha256: a0ce6ed2b346501be1fcae415e4df04618f822834902dc22174a350ae39c791e
  md5: c918f7141733d412f5c579d07f437690
  depends:
  - readline
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - readline >=8.2,<9.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 1929937
  timestamp: 1748631191479
- conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.44-h4bf12b8_1.conda
  sha256: 8556847f91a85c31ef65b05b7e9182a52775616d5d4e550dfb48cdee5fd35687
  md5: e45cfedc8ca5630e02c106ea36d2c5c6
  depends:
  - ld_impl_linux-64 2.44 h1423503_1
  - sysroot_linux-64
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 3781716
  timestamp: 1752032761608
- conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_linux-64-2.44-h4852527_1.conda
  sha256: fbd94448d099a8c5fe7d9ec8c67171ab6e2f4221f453fe327de9b5aaf507f992
  md5: 38e0be090e3af56e44a9cac46101f6cd
  depends:
  - binutils_impl_linux-64 2.44 h4bf12b8_1
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 36046
  timestamp: 1752032788780
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
  sha256: 837b795a2bb39b75694ba910c13c15fa4998d4bb2a622c214a6a5174b2ae53d1
  md5: 74784ee3d225fc3dca89edb635b4e5cc
  depends:
  - __unix
  license: ISC
  purls: []
  size: 154402
  timestamp: 1754210968730
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
  noarch: generic
  sha256: 7e7bc8e73a2f3736444a8564cbece7216464c00f0bc38e604b0c792ff60d621a
  md5: e5279009e7a7f7edd3cd2880c502b3cc
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi * *_cp312
  license: Python-2.0
  purls: []
  size: 45852
  timestamp: 1749047748072
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cccl_linux-64-12.9.27-0.conda
  sha256: 5ea225926517655bd27b723870a3cfa2c6248d634fd6cabc87553306a34113b9
  md5: 2aa00c417be8ce0fa4024464d4eb162c
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1145685
  timestamp: 1742008547224
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-command-line-tools-12.9.1-0.conda
  sha256: f266d51aa8678c4b5f52f41b5a25a1efea567d22d9a3b567b6fa852409efbe11
  md5: 4fd90fdb52c0618af64f589170215dc2
  depends:
  - cuda-cupti-dev 12.9.79.*
  - cuda-gdb 12.9.79.*
  - cuda-nvdisasm 12.9.88.*
  - cuda-nvprof 12.9.79.*
  - cuda-nvtx 12.9.79.*
  - cuda-sanitizer-api 12.9.79.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16999
  timestamp: 1748726775410
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-compiler-12.9.1-0.conda
  sha256: 30047fb87e2d26b359bd730e355d545596020df28288e1084e57a25332d3732e
  md5: fd4bd897da69c563c6fc242fb6d3a5d4
  depends:
  - __linux
  - cuda-cuobjdump 12.9.82.*
  - cuda-cuxxfilt 12.9.82.*
  - cuda-nvcc 12.9.86.*
  - cuda-nvprune 12.9.82.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16988
  timestamp: 1748726786031
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-crt-dev_linux-64-12.9.86-0.conda
  sha256: 29489e67e5e694f96fe77bf1e1a067c1e82614b13c50c4a20f84af2436a3b2fe
  md5: d01efc405de70c839785fa00dd53efef
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 85644
  timestamp: 1748341091593
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-crt-tools-12.9.86-0.conda
  sha256: c187f0482242f5927c43a4c06c09fce05e3367f648d2914081e1e8333eda06f0
  md5: ce820662fcc4d71e8d9ca25d74bb87fd
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 20170
  timestamp: 1748341096049
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-12.9.79-0.conda
  sha256: 1f5d5b30490a10460ac611d3a5d699a020e8f4a2dd050b8be824ab5c2fbb9064
  md5: 57407109c2f355b53d4ca9b7e88cd77b
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart_linux-64 12.9.79 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17809
  timestamp: 1747087796318
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-dev-12.9.79-0.conda
  sha256: e265f3131401bbac57cd882257d8db5f9610b892095e5375bd6b84f6899b832e
  md5: 48a01a7a691c5ba1b7ca3c092f9f39f6
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart 12.9.79 0
  - cuda-cudart-dev_linux-64 12.9.79 0
  - cuda-cudart-static 12.9.79 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17879
  timestamp: 1747087813603
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-dev_linux-64-12.9.79-0.conda
  sha256: 1e6c17b64b5efd5b1b76346de84247a2e06094a6a4c81918591efe4b376ec8c3
  md5: 33030d4dbb7f53f890accc062f7622b4
  depends:
  - cuda-cccl_linux-64
  - cuda-cudart-static_linux-64
  - cuda-cudart_linux-64
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 382904
  timestamp: 1747087800219
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cudart-static-12.9.79-0.conda
  sha256: ea63f85db27fd60fc2f45837ea7fecfb509a870517029b75bfcd2b61b7343f51
  md5: 65d7cf174579790c466808b9578aabc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart-static_linux-64 12.9.79 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17515
  timestamp: 1747087805632
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart-static_linux-64-12.9.79-0.conda
  sha256: d9e552b81925bb5b2327754bf4451b5d454ef48531479d24d366a30049e2a1be
  md5: 3b729224bbd69d2b4b87eeae11dbcaff
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1145537
  timestamp: 1747087781238
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-cudart_linux-64-12.9.79-0.conda
  sha256: 2e227e5dd6822a048d98e4a32219277db55b5c10be0cfb9af27ab5ebce451b78
  md5: b57936ca7e409053b5d325ac2f1b7b2f
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 193644
  timestamp: 1747087788385
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuobjdump-12.9.82-1.conda
  sha256: 2a943c0bcaa8c5d7f7ecd8df178e2fed667197d0ab2b2a2ce14be98ad2048cc0
  md5: 5ba25de243f2208dcf9db6e76d7eda40
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvdisasm
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 246738
  timestamp: 1747676058195
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-12.9.79-0.conda
  sha256: b586bf782a3ca5f26b33671bff63af634087c5c810d82fce9dcb17ad89d01cbf
  md5: 256ad7f05454c8b7c7bd7c3245a00ee4
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1870657
  timestamp: 1747088236280
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cupti-dev-12.9.79-0.conda
  sha256: 74f014e702f5d275dd2da4704b55d6e122ab081e4c2a816ba462a20cdf8527ad
  md5: 79293a395919fce3ef0b07ab3addbeae
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cupti 12.9.79 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - cuda-cupti-static >=12.9.79
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 4283344
  timestamp: 1747088284749
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-cuxxfilt-12.9.82-1.conda
  sha256: 8f0e3a506db86c76f65b347cb4e409a812d352aec29814dcd7d351763dbb4a46
  md5: 6f9d2219775051c2bc6aafee231252c1
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 213812
  timestamp: 1747676128406
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-driver-dev-12.9.79-0.conda
  sha256: 35adac2c4cb5886e9705afcac599426f9f93df27e8e7a6efe7bed74f9aadf3ba
  md5: 17974dd3729bad5cb32cb86bbb1f6e5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-driver-dev_linux-64
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17328
  timestamp: 1747087809629
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-driver-dev_linux-64-12.9.79-0.conda
  sha256: 335ab74cb54b1e7230407a26535d7511ff9dc557a85a005c0144a5be002cbbfa
  md5: 2e8dfd3518d90e296282dbb9e68631a1
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 32177
  timestamp: 1747087792527
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-gdb-12.9.79-1.conda
  sha256: 05db1b3590b74c8bec6b5b5c61d870e0ca356a91f4ca70c94de1ebb308126fb9
  md5: 5d17585b9150afe1059293d88a4afa03
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - gmp >=6.3.0,<7.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 382710
  timestamp: 1747088973123
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-12.9.1-0.conda
  sha256: f4587be23ca8d9f20327620f40591f6583a692356a5bbb150051accf93217bf6
  md5: 1783288a6aad6ea082bdc3ceea46ce45
  depends:
  - cuda-cudart 12.9.79.*
  - cuda-nvrtc 12.9.86.*
  - cuda-opencl 12.9.19.*
  - libcublas 12.9.1.4.*
  - libcufft 11.4.1.4.*
  - libcufile 1.14.1.1.*
  - libcurand 10.3.10.19.*
  - libcusolver 11.7.5.82.*
  - libcusparse 12.5.10.65.*
  - libnpp 12.4.1.87.*
  - libnvfatbin 12.9.82.*
  - libnvjitlink 12.9.86.*
  - libnvjpeg 12.4.0.76.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17025
  timestamp: 1748726808178
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-libraries-dev-12.9.1-0.conda
  sha256: 24930827fbb6a1cf73c549856985a799b069c7b4e33ef4432f8344c4cfcc8e12
  md5: 4c38d8270dcbc4b2556bad4ccc10ca18
  depends:
  - cuda-cccl_linux-64 12.9.27.*
  - cuda-cudart-dev 12.9.79.*
  - cuda-driver-dev 12.9.79.*
  - cuda-nvrtc-dev 12.9.86.*
  - cuda-opencl-dev 12.9.19.*
  - cuda-profiler-api 12.9.79.*
  - libcublas-dev 12.9.1.4.*
  - libcufft-dev 11.4.1.4.*
  - libcufile-dev 1.14.1.1.*
  - libcurand-dev 10.3.10.19.*
  - libcusolver-dev 11.7.5.82.*
  - libcusparse-dev 12.5.10.65.*
  - libnpp-dev 12.4.1.87.*
  - libnvfatbin-dev 12.9.82.*
  - libnvjitlink-dev 12.9.86.*
  - libnvjpeg-dev 12.4.0.76.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17085
  timestamp: 1748726818742
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nsight-12.9.79-0.conda
  sha256: f603cf123a6c0c677d74e67e33cd289606b69381d2214f65baa40c089922a4ae
  md5: 6839c31eda7373a16f37ff9421dda3eb
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 118690550
  timestamp: 1747087008714
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-12.9.86-0.conda
  sha256: e672639fc2d5e7801175e40cbb36e9116c19b41bb6e4fc9c617ead1dac08b1c4
  md5: bea9f6c891386ec466e218a0998189b3
  depends:
  - cuda-nvcc_linux-64 12.9.86.*
  - gcc_linux-64
  - gxx_linux-64
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16898
  timestamp: 1748341333259
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvcc-dev_linux-64-12.9.86-0.conda
  sha256: 16bcfd03616fb320563b203bb6f5730da35686c2c4a847fdaf1950f98023eb9f
  md5: c1348b5ba3dd8bb4cead762aa2cf8a91
  depends:
  - cuda-crt-dev_linux-64 12.9.86 0
  - cuda-nvvm-dev_linux-64 12.9.86 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=6
  constrains:
  - gcc_impl_linux-64 >=6,<14.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 14498270
  timestamp: 1748341256788
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-impl-12.9.86-0.conda
  sha256: 65da9328e129388b5ed61cb6d4dc8d7ae1cc6052eb90b3ca7aeb826939f858d5
  md5: 83fc7dbd01cd61f8805ad8b90af655a7
  depends:
  - cuda-cudart >=12.9.37,<13.0a0
  - cuda-cudart-dev
  - cuda-nvcc-dev_linux-64 12.9.86 0
  - cuda-nvcc-tools 12.9.86 0
  - cuda-nvvm-impl 12.9.86 0
  - cuda-version >=12.9,<12.10.0a0
  constrains:
  - gcc_impl_linux-64 >=6,<14.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 19729
  timestamp: 1748341299340
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc-tools-12.9.86-0.conda
  sha256: 34247d80e740093e0e4a251423b051b5722b37a487b607002a705234d802ddc3
  md5: 615fd0779a86d0689646116d239f57ab
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-crt-tools 12.9.86 0
  - cuda-nvvm-tools 12.9.86 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - gcc_impl_linux-64 >=6,<14.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 27417956
  timestamp: 1748341203204
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvcc_linux-64-12.9.86-0.conda
  sha256: 4053f67a705ac1311e3c06f966b431623098135cd4a3c95cb98211f52eb8ac22
  md5: bbc238801cf930ea97ae3ae8946152fd
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart-dev_linux-64 12.9.*
  - cuda-driver-dev_linux-64 12.9.*
  - cuda-nvcc-dev_linux-64 12.9.86.*
  - cuda-nvcc-impl 12.9.86.*
  - cuda-nvcc-tools 12.9.86.*
  - sysroot_linux-64 >=2.17,<3.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 20054
  timestamp: 1748341331747
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvdisasm-12.9.88-1.conda
  sha256: c5322b69357fd764f3e4ed13a1e5d6bd711339b4079426f7a796bf3175f1dbaf
  md5: 721ec74aff0cc0de99acbb65bf42d903
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 5513931
  timestamp: 1748708156611
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvml-dev-12.9.79-1.conda
  sha256: d1b78a9c7be6643edf951fdd206ad9f58e61f7ce5c4a1953d34d1851861d444b
  md5: 50cc3bdad33a81ea4bf5509c8f657bf7
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 139217
  timestamp: 1747087061747
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprof-12.9.79-0.conda
  sha256: 0d3ae0465faf3de0338d738c28c49d946b87e110f4aa8ce4dcabd30927776e5f
  md5: 98f3c444b951cc139ab010ae101d8c73
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cupti
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 2631321
  timestamp: 1747088601661
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvprune-12.9.82-1.conda
  sha256: 69696be1c94f5d5e6d273f3f4df8a705f6fc320b554f35ddacc179d0fbc243c1
  md5: 883401b8c18dff31684cda10512d474f
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 66660
  timestamp: 1747675622707
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-12.9.86-0.conda
  sha256: 7c647fe502b50b40c44696764d39c7b362974075ab341ea3321d5c34aefa0073
  md5: 7b70b28986e13d88cb3d94209c0b215c
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 67191718
  timestamp: 1748340611254
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvrtc-dev-12.9.86-0.conda
  sha256: 83f9f5f40f50ce88f61050e48e70ee00c0475b34f5b6f9257d330fd2b0687549
  md5: 759e8e126cf8b5de1eb4d3683a382db0
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvrtc 12.9.86 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - cuda-nvrtc-static >=12.9.86
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 31311
  timestamp: 1748340858791
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvtx-12.9.79-0.conda
  sha256: c22cb94e1aa789809b7f39674bc767b728499665a3b10aafe372db6f110bfade
  md5: fd5b5f7d3a665bce833a4d0e38323c59
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 24556
  timestamp: 1747088461571
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-nvvm-dev_linux-64-12.9.86-0.conda
  sha256: 3be90ebbbbb208243fe8d0669a77b7500f72c874345eb7aab09f86f9681c9e7a
  md5: d2e312d73a70d8cc629412aca03d887c
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 18208
  timestamp: 1748341099050
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-impl-12.9.86-0.conda
  sha256: 77277cdd8b8cb70ea695771749f3dee928ea67c1e57e2e072cb2251c9be72e6f
  md5: a54efc83c79e0a73abf0294df158f403
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 21462441
  timestamp: 1748341109454
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvm-tools-12.9.86-0.conda
  sha256: 199e68950623c30c419eedf4dba2f88a245365e1e0e7602d71ee77a58864d06e
  md5: bb82494b32a7984e8e99e7199a3f7c1e
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 24277499
  timestamp: 1748341151820
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-nvvp-12.9.79-1.conda
  sha256: 28f8081310ec7581d87c165a08c59ab40f21066dd584fe431c61e1c8d2ec6eb9
  md5: 52b1d6a5fd60d9424df8005916c4ecda
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvdisasm
  - cuda-nvprof
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 117894210
  timestamp: 1747090299872
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-12.9.19-0.conda
  sha256: f91739c5b97e2b31684a5c9eef99dce247c090796fc7293d5b0df3c2d9f54447
  md5: 3c20df1172e66379f7717bedf8ac16d4
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  - ocl-icd >=2.3.2,<3.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 25946
  timestamp: 1741064030231
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-opencl-dev-12.9.19-0.conda
  sha256: 9205a98b2c367adbfdf1a7ee14c40ce459c0687f6a33befdc40610c78536138f
  md5: 657fc5d9fb70d1a68d635880be91e6b0
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-opencl 12.9.19 0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 92890
  timestamp: 1741064033745
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-profiler-api-12.9.79-0.conda
  sha256: 99420cdcfd7c4125a1344ae9e03b95f5b9c3fbe49644b85c20d52e8d80e8fd8b
  md5: 2e6f6485040b90e33a9061470e0dc801
  depends:
  - cuda-cudart-dev
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 19627
  timestamp: 1747088943240
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-sanitizer-api-12.9.79-1.conda
  sha256: 706001ddeb290feb77dd0d7d1549ec632af2818d5d375094f2ca04e00e9b9c87
  md5: 2848e28e5837841bc66ba147b1521f01
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 9245840
  timestamp: 1747087365084
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-toolkit-12.9.1-0.conda
  sha256: af75d891edff584548b7d4056febb4c835ee60248710f9645c4f09fa8a139eb4
  md5: aa08278b20a98d63e18e04134d1dd4c9
  depends:
  - __linux
  - cuda-compiler 12.9.1.*
  - cuda-libraries 12.9.1.*
  - cuda-libraries-dev 12.9.1.*
  - cuda-nvml-dev 12.9.79.*
  - cuda-tools 12.9.1.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16944
  timestamp: 1748726879617
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-tools-12.9.1-0.conda
  sha256: 20e9ed2f72aedcfe0b5a64bfd016be46a66f67e912a6c7300fcb71cc35d1965f
  md5: a6bd4cc7f5689b7a143060984679f67d
  depends:
  - cuda-command-line-tools 12.9.1.*
  - cuda-visual-tools 12.9.1.*
  - gds-tools 1.14.1.1.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16922
  timestamp: 1748726870052
- conda: https://conda.anaconda.org/nvidia/noarch/cuda-version-12.9-3.conda
  sha256: d05c0d4e2d1fbf32275db798275b9a8b57e97c0748f4c073ba6c532fe839bb06
  md5: 40969c18662ba1d35292e70e8545ce90
  constrains:
  - __cuda >=12
  - cudatoolkit 12.9|12.9.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 17161
  timestamp: 1748727273518
- conda: https://conda.anaconda.org/nvidia/linux-64/cuda-visual-tools-12.9.1-0.conda
  sha256: a94f0d7e1e27a32c2ed6f523702fd3a51f742fadfbdf44e4302d0b968c86cdd8
  md5: 63669ced86c4c0d1465d71d966efc28d
  depends:
  - cuda-libraries-dev 12.9.1.*
  - cuda-nsight 12.9.79.*
  - cuda-nvml-dev 12.9.79.*
  - cuda-nvvp 12.9.79.*
  - nsight-compute 2025.2.1.3.*
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 16970
  timestamp: 1748726838833
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.1-hecca717_0.conda
  sha256: e981cf62a722f0eb4631ac7b786c288c03883fbc241fa98a276308fb69cb2c59
  md5: 6033d8c2bb9b460929d00ba54154614c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat 2.7.1 hecca717_0
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 140948
  timestamp: 1752719584725
- pypi: https://files.pythonhosted.org/packages/42/14/42b2651a2f46b022ccd948bca9f2d5af0fd8929c4eec235b8d6d844fbe67/filelock-3.19.1-py3-none-any.whl
  name: filelock
  version: 3.19.1
  sha256: d38e30481def20772f5baf097c122c3babc4fcdb7e14e57049eb9d88c6dc017d
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 172450
  timestamp: 1745369996765
- pypi: https://files.pythonhosted.org/packages/47/71/70db47e4f6ce3e5c37a607355f80da8860a33226be640226ac52cb05ef2e/fsspec-2025.9.0-py3-none-any.whl
  name: fsspec
  version: 2025.9.0
  sha256: 530dc2a2af60a414a832059574df4a6e10cce927f6f4a78209390fe38955cfb7
  requires_dist:
  - adlfs ; extra == 'abfs'
  - adlfs ; extra == 'adl'
  - pyarrow>=1 ; extra == 'arrow'
  - dask ; extra == 'dask'
  - distributed ; extra == 'dask'
  - pre-commit ; extra == 'dev'
  - ruff>=0.5 ; extra == 'dev'
  - numpydoc ; extra == 'doc'
  - sphinx ; extra == 'doc'
  - sphinx-design ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - yarl ; extra == 'doc'
  - dropbox ; extra == 'dropbox'
  - dropboxdrivefs ; extra == 'dropbox'
  - requests ; extra == 'dropbox'
  - adlfs ; extra == 'full'
  - aiohttp!=4.0.0a0,!=4.0.0a1 ; extra == 'full'
  - dask ; extra == 'full'
  - distributed ; extra == 'full'
  - dropbox ; extra == 'full'
  - dropboxdrivefs ; extra == 'full'
  - fusepy ; extra == 'full'
  - gcsfs ; extra == 'full'
  - libarchive-c ; extra == 'full'
  - ocifs ; extra == 'full'
  - panel ; extra == 'full'
  - paramiko ; extra == 'full'
  - pyarrow>=1 ; extra == 'full'
  - pygit2 ; extra == 'full'
  - requests ; extra == 'full'
  - s3fs ; extra == 'full'
  - smbprotocol ; extra == 'full'
  - tqdm ; extra == 'full'
  - fusepy ; extra == 'fuse'
  - gcsfs ; extra == 'gcs'
  - pygit2 ; extra == 'git'
  - requests ; extra == 'github'
  - gcsfs ; extra == 'gs'
  - panel ; extra == 'gui'
  - pyarrow>=1 ; extra == 'hdfs'
  - aiohttp!=4.0.0a0,!=4.0.0a1 ; extra == 'http'
  - libarchive-c ; extra == 'libarchive'
  - ocifs ; extra == 'oci'
  - s3fs ; extra == 's3'
  - paramiko ; extra == 'sftp'
  - smbprotocol ; extra == 'smb'
  - paramiko ; extra == 'ssh'
  - aiohttp!=4.0.0a0,!=4.0.0a1 ; extra == 'test'
  - numpy ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-asyncio!=0.22.0 ; extra == 'test'
  - pytest-benchmark ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-mock ; extra == 'test'
  - pytest-recording ; extra == 'test'
  - pytest-rerunfailures ; extra == 'test'
  - requests ; extra == 'test'
  - aiobotocore>=2.5.4,<3.0.0 ; extra == 'test-downstream'
  - dask[dataframe,test] ; extra == 'test-downstream'
  - moto[server]>4,<5 ; extra == 'test-downstream'
  - pytest-timeout ; extra == 'test-downstream'
  - xarray ; extra == 'test-downstream'
  - adlfs ; extra == 'test-full'
  - aiohttp!=4.0.0a0,!=4.0.0a1 ; extra == 'test-full'
  - cloudpickle ; extra == 'test-full'
  - dask ; extra == 'test-full'
  - distributed ; extra == 'test-full'
  - dropbox ; extra == 'test-full'
  - dropboxdrivefs ; extra == 'test-full'
  - fastparquet ; extra == 'test-full'
  - fusepy ; extra == 'test-full'
  - gcsfs ; extra == 'test-full'
  - jinja2 ; extra == 'test-full'
  - kerchunk ; extra == 'test-full'
  - libarchive-c ; extra == 'test-full'
  - lz4 ; extra == 'test-full'
  - notebook ; extra == 'test-full'
  - numpy ; extra == 'test-full'
  - ocifs ; extra == 'test-full'
  - pandas ; extra == 'test-full'
  - panel ; extra == 'test-full'
  - paramiko ; extra == 'test-full'
  - pyarrow ; extra == 'test-full'
  - pyarrow>=1 ; extra == 'test-full'
  - pyftpdlib ; extra == 'test-full'
  - pygit2 ; extra == 'test-full'
  - pytest ; extra == 'test-full'
  - pytest-asyncio!=0.22.0 ; extra == 'test-full'
  - pytest-benchmark ; extra == 'test-full'
  - pytest-cov ; extra == 'test-full'
  - pytest-mock ; extra == 'test-full'
  - pytest-recording ; extra == 'test-full'
  - pytest-rerunfailures ; extra == 'test-full'
  - python-snappy ; extra == 'test-full'
  - requests ; extra == 'test-full'
  - smbprotocol ; extra == 'test-full'
  - tqdm ; extra == 'test-full'
  - urllib3 ; extra == 'test-full'
  - zarr ; extra == 'test-full'
  - zstandard ; python_full_version < '3.14' and extra == 'test-full'
  - tqdm ; extra == 'tqdm'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.4.0-h69c5793_4.conda
  sha256: 1e7730351cc2feb3213d9e5e6730158154d616e74eca52578eae869be628ecc4
  md5: 3beb12e7e1d178ec50e9b78788bad12a
  depends:
  - binutils_impl_linux-64 >=2.40
  - libgcc >=13.4.0
  - libgcc-devel_linux-64 13.4.0 hba01cd7_104
  - libgomp >=13.4.0
  - libsanitizer 13.4.0 h14bf0c3_4
  - libstdcxx >=13.4.0
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 65471629
  timestamp: 1753903083063
- conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_linux-64-13.4.0-h621f4e2_11.conda
  sha256: ca4647bd3d75f401654afc029c1e187a1b855a51567e1350c55a8320f3edbd82
  md5: 0ebd7e4272f1dfb557b02dacb1829d94
  depends:
  - binutils_linux-64
  - gcc_impl_linux-64 13.4.0.*
  - sysroot_linux-64
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 32490
  timestamp: 1753912257961
- conda: https://conda.anaconda.org/nvidia/linux-64/gds-tools-1.14.1.1-4.conda
  sha256: 6effde8c2765613ff989c345adf75e8b90e6cdae3383fe856ba6bf90fbf7418f
  md5: 3f556bbee6502c75569410b0b56ce710
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcufile >=1.14.1.1,<2.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 39573423
  timestamp: 1747331964077
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.4.0-haf17267_4.conda
  sha256: d4be0fb94c788b039f8651f05757310c8b20fc3cc8bd5b4dff9ce6d232bac0fc
  md5: 3439b41a67a4764c0e361dbb15e918b8
  depends:
  - gcc_impl_linux-64 13.4.0 h69c5793_4
  - libstdcxx-devel_linux-64 13.4.0 hba01cd7_104
  - sysroot_linux-64
  - tzdata
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 13269750
  timestamp: 1753903303961
- conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_linux-64-13.4.0-he431e45_11.conda
  sha256: 8767c882aa49fd1054e66ec0f962ff32b2c4ba8d9d8648a315a2eb894f33e609
  md5: e2734690f868585c00aba10a6e32b9b7
  depends:
  - binutils_linux-64
  - gcc_linux-64 13.4.0 h621f4e2_11
  - gxx_impl_linux-64 13.4.0.*
  - sysroot_linux-64
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 30825
  timestamp: 1753912276744
- pypi: https://files.pythonhosted.org/packages/62/a1/3d680cbfd5f4b8f15abc1d571870c5fc3e594bb582bc3b64ea099db13e56/jinja2-3.1.6-py3-none-any.whl
  name: jinja2
  version: 3.1.6
  sha256: 85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67
  requires_dist:
  - markupsafe>=2.0
  - babel>=2.7 ; extra == 'i18n'
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
  sha256: 305c22a251db227679343fd73bfde121e555d466af86e537847f4c8b9436be0d
  md5: ff007ab0f0fdc53d245972bba8a6d40c
  constrains:
  - sysroot_linux-64 ==2.28
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 1272697
  timestamp: 1752669126073
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
  sha256: 1a620f27d79217c1295049ba214c2f80372062fd251b569e9873d4a953d27554
  md5: 0be7c6e070c19105f966d3758448d018
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.44
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 676044
  timestamp: 1752032747103
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-34_h59b9bed_openblas.conda
  build_number: 34
  sha256: 08a394ba934f68f102298259b150eb5c17a97c30c6da618e1baab4247366eab3
  md5: 064c22bac20fecf2a99838f9b979374c
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - mkl <2025
  - blas 2.134   openblas
  - liblapacke 3.9.0   34*_openblas
  - libcblas   3.9.0   34*_openblas
  - liblapack  3.9.0   34*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 19306
  timestamp: 1754678416811
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-34_he106b2a_openblas.conda
  build_number: 34
  sha256: edde454897c7889c0323216516abb570a593de728c585b14ef41eda2b08ddf3a
  md5: 148b531b5457ad666ed76ceb4c766505
  depends:
  - libblas 3.9.0 34_h59b9bed_openblas
  constrains:
  - liblapacke 3.9.0   34*_openblas
  - blas 2.134   openblas
  - liblapack  3.9.0   34*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 19313
  timestamp: 1754678426220
- conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-12.9.1.4-0.conda
  sha256: f97f9ccff5ab335da2deb6353bdb08e968872aab88a45a6ec25352a4897c3839
  md5: 21c21de732ef68ff609bf9257674f95a
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvrtc
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 468020298
  timestamp: 1748715326784
- conda: https://conda.anaconda.org/nvidia/linux-64/libcublas-dev-12.9.1.4-0.conda
  sha256: 9257cf47932e6d5826311b1f399f13cf3bc55207248a92f8728770a678a6825f
  md5: a3c7620ce08de9746066b7d43126deb9
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-crt-dev_linux-64
  - cuda-cudart-dev_linux-64
  - cuda-version >=12.9,<12.10.0a0
  - libcublas 12.9.1.4 0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcublas-static >=12.9.1.4
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 88507
  timestamp: 1748716005357
- conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-11.4.1.4-0.conda
  sha256: 797435910d8d1daad64a0c5464f2d37d1cf4b6a730c0932194410f8347fd86f3
  md5: 8bdfa60eb3a6cc642bab09bf1cbdcaf9
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 162314009
  timestamp: 1748192932195
- conda: https://conda.anaconda.org/nvidia/linux-64/libcufft-dev-11.4.1.4-0.conda
  sha256: 1be4dc6261a78f0464e78934a2c2581e06bf1c47847d26a5883c311514d32384
  md5: d876f8814daaed5f34e1255ef5349b91
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcufft 11.4.1.4 0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcufft-static >=11.4.1.4
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30030
  timestamp: 1748193295984
- conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-1.14.1.1-4.conda
  sha256: af949acbf3164ef934f1c23b1ec85577e6a0adb8a6a2bb63c2535f9adea7109d
  md5: 8daddcaa66bbcb878552c4bf37599ca8
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 969008
  timestamp: 1747331940764
- conda: https://conda.anaconda.org/nvidia/linux-64/libcufile-dev-1.14.1.1-4.conda
  sha256: fc9fa521733a526c09bc16bab869af5baf951b8a9fd5156330cb27e1eab347b9
  md5: 068f6c495bff39747855f611714b5829
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcufile 1.14.1.1 4
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcufile-static >=1.14.1.1
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30930
  timestamp: 1747331959737
- conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-10.3.10.19-0.conda
  sha256: 65d4f3e3286af1165679baee2cb81ba518a186d27362293321c8cfe1317f87b6
  md5: 21dcbdfb0482a369aab2b1e0abb6a399
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 46145899
  timestamp: 1741064115039
- conda: https://conda.anaconda.org/nvidia/linux-64/libcurand-dev-10.3.10.19-0.conda
  sha256: b0f5197bccc12ec0449d252e0f0b07ca843f687c63aa06362934cd2f78a4328c
  md5: 8a50519a4292616353b00d336af3e705
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcurand 10.3.10.19 0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcurand-static >=10.3.10.19
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 243843
  timestamp: 1741064211694
- conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-11.7.5.82-0.conda
  sha256: 2e1b3b6f7e344b8ca8f6204c9c59d4552d7515973c9c34876f4526b1c5b85114
  md5: 66fd03151aab73c09f995f59afa171b7
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcublas
  - libcusparse
  - libgcc-ng >=11.2.0
  - libnvjitlink
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 205218362
  timestamp: 1747690675371
- conda: https://conda.anaconda.org/nvidia/linux-64/libcusolver-dev-11.7.5.82-0.conda
  sha256: f633a717b8b91a50b5b67346dd89a7bfe1a18a99678ce4b1bdba85d3043e1db8
  md5: d94ac9d6d0d1e24c248818f8e994b897
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcusolver 11.7.5.82 0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcusolver-static >=11.7.5.82
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 57998
  timestamp: 1747690948372
- conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-12.5.10.65-0.conda
  sha256: 586a0fd3a52edd871c46c62f7ed7dd69dbe0033c9765e129222333e195670282
  md5: 18174bcd9c9889589e77c27aa776bff1
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libnvjitlink
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 208943628
  timestamp: 1747092698953
- conda: https://conda.anaconda.org/nvidia/linux-64/libcusparse-dev-12.5.10.65-0.conda
  sha256: e648722677546b3ea320eaa3397a10dbe575fca4c08898677e428d3b61a7c3fc
  md5: 3fb033ca318e4752c538f2f2e592c824
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcusparse 12.5.10.65 0
  - libgcc-ng >=11.2.0
  - libnvjitlink
  - libstdcxx-ng >=11.2.0
  constrains:
  - libcusparse-static >=12.5.10.65
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 47437
  timestamp: 1747093040207
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
  sha256: da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2
  md5: 4211416ecba1866fab0c6470986c22d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74811
  timestamp: 1752719572741
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_4.conda
  sha256: 144e35c1c2840f2dc202f6915fc41879c19eddbb8fa524e3ca4aa0d14018b26f
  md5: f406dcbb2e7bef90d793e50e79a2882b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_4
  - libgomp 15.1.0 h767d61c_4
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 824153
  timestamp: 1753903866511
- conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.4.0-hba01cd7_104.conda
  sha256: 016f57cf3d95088b03d0cccb887914d0d8a2f6bd681c3ea2b97524bc1c1d6da7
  md5: 3cb7a6f6193b4884528317d5da46f459
  depends:
  - __unix
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 2573451
  timestamp: 1753902877889
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_4.conda
  sha256: 76ceac93ed98f208363d6e9c75011b0ff7b97b20f003f06461a619557e726637
  md5: 28771437ffcd9f3417c66012dc49a3be
  depends:
  - libgcc 15.1.0 h767d61c_4
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29249
  timestamp: 1753903872571
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_4.conda
  sha256: 2fe41683928eb3c57066a60ec441e605a69ce703fc933d6d5167debfeba8a144
  md5: 53e876bc2d2648319e94c33c57b9ec74
  depends:
  - libgfortran5 15.1.0 hcea5267_4
  constrains:
  - libgfortran-ng ==15.1.0=*_4
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29246
  timestamp: 1753903898593
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_4.conda
  sha256: 3070e5e2681f7f2fb7af0a81b92213f9ab430838900da8b4f9b8cf998ddbdd84
  md5: 8a4ab7ff06e4db0be22485332666da0f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1564595
  timestamp: 1753903882088
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.3-h1fed272_1.conda
  sha256: 171aabf2dc14fe24176ae74be6bea6ea7f92f9d7cd3982761babea797352da3e
  md5: 0896dfc882f5a701dbc20c8b0058ce7d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=14
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.46,<10.47.0a0
  constrains:
  - glib 2.84.3 *_1
  license: LGPL-2.1-or-later
  purls: []
  size: 3978685
  timestamp: 1756821711050
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_4.conda
  sha256: e0487a8fec78802ac04da0ac1139c3510992bc58a58cde66619dde3b363c2933
  md5: 3baf8976c96134738bba224e9ef6b1e5
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 447289
  timestamp: 1753903801049
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h3b78370_2.conda
  sha256: c467851a7312765447155e071752d7bf9bf44d610a5687e32706f480aad2833f
  md5: 915f5995e94f60e9a4826e0b0920ee88
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: LGPL-2.1-only
  purls: []
  size: 790176
  timestamp: 1754908768807
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-34_h7ac8fdf_openblas.conda
  build_number: 34
  sha256: 9c941d5da239f614b53065bc5f8a705899326c60c9f349d9fbd7bd78298f13ab
  md5: f05a31377b4d9a8d8740f47d1e70b70e
  depends:
  - libblas 3.9.0 34_h59b9bed_openblas
  constrains:
  - liblapacke 3.9.0   34*_openblas
  - libcblas   3.9.0   34*_openblas
  - blas 2.134   openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 19324
  timestamp: 1754678435277
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_2.conda
  sha256: 329e66330a8f9cbb6a8d5995005478188eb4ba8a6b6391affa849744f4968492
  md5: f61edadbb301530bd65a32646bd81552
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - liblzma 5.8.1 hb9d3cd8_2
  license: 0BSD
  purls: []
  size: 439868
  timestamp: 1749230061968
- conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-12.4.1.87-0.conda
  sha256: 50e5e90742140c64006af03857aa603ee280d3e4dab7d2583ffaede585221b68
  md5: 6a87f8edc9f58733aeb398f2a4b88bd2
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 175699456
  timestamp: 1747091236055
- conda: https://conda.anaconda.org/nvidia/linux-64/libnpp-dev-12.4.1.87-0.conda
  sha256: 932792d984e02eb82a6062b4e9d4d350aa0cf166b7ebd0f27249bbcf449feca6
  md5: c08bcd1cff9638a8b197725fbd8171b7
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libnpp 12.4.1.87 0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libnpp-static >=12.4.1.87
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 452766
  timestamp: 1747091522282
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
  sha256: 927fe72b054277cde6cb82597d0fcf6baf127dcbce2e0a9d8925a68f1265eef5
  md5: d864d34357c3b65a4b731f78c0801dc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33731
  timestamp: 1750274110928
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-12.9.82-0.conda
  sha256: 9ab2e1f76585ac29915d9631b4ecc28e2bf6659a500dcc718a33b19851dab7ae
  md5: e2782aff94e5eec6bcc7dc378b160359
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 818218
  timestamp: 1747676172927
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvfatbin-dev-12.9.82-0.conda
  sha256: 0068523ad1bddbad7249032f5c3c3f096f303d7a5cd38d9a8f6bc8e0ba2a45e4
  md5: 07fb4aa58bfe48f471881493966f98d9
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libnvfatbin 12.9.82 0
  - libstdcxx-ng >=11.2.0
  constrains:
  - liblibnvfatbin-static >=12.9.82
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 22161
  timestamp: 1747676184094
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-12.9.86-0.conda
  sha256: 5fe20c503410d72fcfd475fefe97c9ca05ddf78728eb4e631411e9a0b44767cb
  md5: a3ea5bfb49d231a7e1b73a1dc504cbb4
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30592664
  timestamp: 1748341083485
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvjitlink-dev-12.9.86-0.conda
  sha256: 2e591a21a319bc3dd178005e6b6c12c487ee4fc0203b2bf6fb198f187d1783d3
  md5: e3c3e98e66c5dfe0fc7f8c5def9b0503
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libnvjitlink 12.9.86 0
  - libstdcxx-ng >=11.2.0
  constrains:
  - libnvjitlink-static >=12.9.86
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 22018
  timestamp: 1748341224661
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-12.4.0.76-0.conda
  sha256: 9ab21aabe8e440dd013bd1b9f89db8fc31c6349039ba6801011ac95850327bc8
  md5: 6ccadcc5dd5743b69b127fb9f7f1f6a0
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc-ng >=11.2.0
  - libstdcxx-ng >=11.2.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 3582921
  timestamp: 1747089021195
- conda: https://conda.anaconda.org/nvidia/linux-64/libnvjpeg-dev-12.4.0.76-0.conda
  sha256: ece9d2415a4a1021e8c0c18d6dbe9e562a473aa1b3368481ddd10698d948a0a6
  md5: 85b92b80e174d54f2cc74cc405dd1fc9
  depends:
  - cuda-cudart-dev
  - cuda-version >=12.9,<12.10.0a0
  - libnvjpeg 12.4.0.76 0
  constrains:
  - libnvjpeg-static >=12.4.0.76
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 27785
  timestamp: 1747089031555
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
  sha256: 1b51d1f96e751dc945cc06f79caa91833b0c3326efe24e9b506bd64ef49fc9b0
  md5: dfc5aae7b043d9f56ba99514d5e60625
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 5938936
  timestamp: 1755474342204
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h421ea60_1.conda
  sha256: e75a2723000ce3a4b9fd9b9b9ce77553556c93e475a4657db6ed01abc02ea347
  md5: 7af8e91b0deb5f8e25d1a595dea79614
  depends:
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 317390
  timestamp: 1753879899951
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.4.0-h14bf0c3_4.conda
  sha256: bcfdbb9f0a1ea74468c8a46f6e3dd9093183d207becdd94eb8df4385cc11fedb
  md5: 6b7953966683a477ff8cc35961894fe8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13.4.0
  - libstdcxx >=13.4.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 4595811
  timestamp: 1753903024563
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
  sha256: 6d9c32fc369af5a84875725f7ddfbfc2ace795c28f246dc70055a79f9b2003da
  md5: 0b367fad34931cb79e0d6b7e5c06bb1c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 932581
  timestamp: 1753948484112
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_4.conda
  sha256: b5b239e5fca53ff90669af1686c86282c970dd8204ebf477cf679872eb6d48ac
  md5: 3c376af8888c386b9d3d1c2701e2f3ab
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_4
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3903453
  timestamp: 1753903894186
- conda: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.4.0-hba01cd7_104.conda
  sha256: e77f2f5481c380c2196298259920326867b6ff9757a58101ddc27d570fd84744
  md5: 2c8b4489336bc9a8c8d3f361fd177882
  depends:
  - __unix
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 13988656
  timestamp: 1753902906863
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_4.conda
  sha256: 81c841c1cf4c0d06414aaa38a249f9fdd390554943065c3a0b18a9fb7e8cc495
  md5: 2d34729cbc1da0ec988e57b13b712067
  depends:
  - libstdcxx 15.1.0 h8f9b012_4
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29317
  timestamp: 1753903924491
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.11.0-he8b52b9_0.conda
  sha256: 23f47e86cc1386e7f815fa9662ccedae151471862e971ea511c5c886aa723a54
  md5: 74e91c36d0eef3557915c68b6c2bef96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 791328
  timestamp: 1754703902365
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h2cb61b6_1.conda
  sha256: 2c80ef042b47dfddb1f425d57d367e0657f8477d80111644c88b172ff2f99151
  md5: 42a8e4b54e322b4cd1dbfb30a8a7ce9e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - icu <0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 697020
  timestamp: 1754315347913
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- pypi: https://files.pythonhosted.org/packages/f3/f0/89e7aadfb3749d0f52234a0c8c7867877876e0a20b60e2188e9850794c17/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: markupsafe
  version: 3.0.2
  sha256: e17c96c14e19278594aa4841ec148115f9c7615a47382ecb6b82bd8fea3ab0c8
  requires_python: '>=3.9'
- conda: https://conda.modular.com/max-nightly/linux-64/max-25.6.0.dev2025090405-3.12release.conda
  sha256: c3f65cd6d69a1146138696bd112fc648f367f7bba661a07dffc746db1cb4ae77
  depends:
  - numpy >=1.18
  - typing-extensions >=4.12.2
  - python-gil
  - max-core ==25.6.0.dev2025090405 release
  - python_abi 3.12.* *_cp312
  constrains:
  - click >=8.0.0
  - gguf >=0.17.1
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - jinja2 >=3.1.6
  - llguidance >=1.0.1
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - rich >=13.0.1
  - sentencepiece >=0.2.0
  - tqdm >=4.67.1
  - transformers >=4.55.0
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3
  - grpcio >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0
  - opentelemetry-exporter-prometheus >=0.50b0
  - opentelemetry-sdk >=1.29.0,<1.36.0
  - prometheus_client >=0.21.0
  - protobuf >=6.31.1,<6.32.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - regex >=2024.11.6
  - scipy >=1.13.0
  - sse-starlette >=2.1.2
  - starlette >=0.47.2
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 6117492
  timestamp: 1756963185311
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.6.0.dev2025090405-release.conda
  sha256: 8a869dac1287741b5497aa4152a3ae5252f04cfa31a2bf6616741dabadaea024
  depends:
  - mojo-compiler ==********.dev2025090405 release
  license: LicenseRef-Modular-Proprietary
  size: 64435768
  timestamp: 1756963185310
- conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
  sha256: 7f0bfa9ed2f803f2205bdab5560e2a28ba1da1acf62fbb23963a100f0730dc08
  license: LicenseRef-Modular-Proprietary
  size: 78190498
  timestamp: 1753680256329
- conda: https://conda.modular.com/max-nightly/linux-64/mojo-compiler-********.dev2025090405-release.conda
  sha256: 023a801181563ffc5117f984a891579002c0d172c15297d568c0a5e40bf37aee
  depends:
  - mojo-python ==********.dev2025090405 release
  license: LicenseRef-Modular-Proprietary
  size: 83287393
  timestamp: 1756963185310
- conda: https://conda.modular.com/max-nightly/noarch/mojo-python-********.dev2025090405-release.conda
  noarch: python
  sha256: 10e4176a6da9155b316cafd359cdae5dd660a69268ce516cd79c4dce812cf687
  depends:
  - python
  license: LicenseRef-Modular-Proprietary
  size: 17438
  timestamp: 1756963227080
- pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
  name: mpmath
  version: 1.3.0
  sha256: a0b2b9fe80bbcd81a6647ff13108738cfb482d481d826cc0e02f5b35e5c88d2c
  requires_dist:
  - pytest>=4.6 ; extra == 'develop'
  - pycodestyle ; extra == 'develop'
  - pytest-cov ; extra == 'develop'
  - codecov ; extra == 'develop'
  - wheel ; extra == 'develop'
  - sphinx ; extra == 'docs'
  - gmpy2>=2.1.0a4 ; platform_python_implementation != 'PyPy' and extra == 'gmpy'
  - pytest>=4.6 ; extra == 'tests'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- pypi: https://files.pythonhosted.org/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl
  name: networkx
  version: '3.5'
  sha256: 0030d386a9a06dee3565298b4a734b68589749a544acbb6c412dc9e2489ec6ec
  requires_dist:
  - numpy>=1.25 ; extra == 'default'
  - scipy>=1.11.2 ; extra == 'default'
  - matplotlib>=3.8 ; extra == 'default'
  - pandas>=2.0 ; extra == 'default'
  - pre-commit>=4.1 ; extra == 'developer'
  - mypy>=1.15 ; extra == 'developer'
  - sphinx>=8.0 ; extra == 'doc'
  - pydata-sphinx-theme>=0.16 ; extra == 'doc'
  - sphinx-gallery>=0.18 ; extra == 'doc'
  - numpydoc>=1.8.0 ; extra == 'doc'
  - pillow>=10 ; extra == 'doc'
  - texext>=0.6.7 ; extra == 'doc'
  - myst-nb>=1.1 ; extra == 'doc'
  - intersphinx-registry ; extra == 'doc'
  - osmnx>=2.0.0 ; extra == 'example'
  - momepy>=0.7.2 ; extra == 'example'
  - contextily>=1.6 ; extra == 'example'
  - seaborn>=0.13 ; extra == 'example'
  - cairocffi>=1.7 ; extra == 'example'
  - igraph>=0.11 ; extra == 'example'
  - scikit-learn>=1.5 ; extra == 'example'
  - lxml>=4.6 ; extra == 'extra'
  - pygraphviz>=1.14 ; extra == 'extra'
  - pydot>=3.0.1 ; extra == 'extra'
  - sympy>=1.10 ; extra == 'extra'
  - pytest>=7.2 ; extra == 'test'
  - pytest-cov>=4.0 ; extra == 'test'
  - pytest-xdist>=3.0 ; extra == 'test'
  - pytest-mpl ; extra == 'test-extras'
  - pytest-randomly ; extra == 'test-extras'
  requires_python: '>=3.11'
- conda: https://conda.anaconda.org/nvidia/linux-64/nsight-compute-2025.2.1.3-0.conda
  sha256: acdabd89efb74923c164c0771f9627e18c42955908276ed511cd6309895edec3
  md5: 7989572b928d897a4ff2448c8b64d563
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - dbus >=1.13.18,<2.0a0
  - expat >=2.7.1,<3.0a0
  - fontconfig >=2.14.1,<3.0a0
  - freetype >=2.13.3,<3.0a0
  - libgcc-ng >=11.2.0
  - libglib >=2.78.4,<3.0a0
  - libstdcxx-ng >=11.2.0
  - libxkbcommon >=1.9.1,<2.0a0
  - nspr >=4.35,<5.0a0
  - nss >=3.89.1,<4.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 334801822
  timestamp: 1747348445463
- conda: https://conda.anaconda.org/conda-forge/linux-64/nspr-4.37-h29cc59b_0.conda
  sha256: 472306630dcd49a221863b91bd89f5b8b81daf1b99adf1968c9f12021176d873
  md5: d73ccc379297a67ed921bd55b38a6c6a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 230951
  timestamp: 1752841107697
- conda: https://conda.anaconda.org/conda-forge/linux-64/nss-3.115-hc3c8bcf_0.conda
  sha256: d57e18a97fe89efffa419843f994be5cb03da40728398fa388090111f59083d5
  md5: c8873d2f90ad15aaec7be6926f11b53d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libsqlite >=3.50.4,<4.0a0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - nspr >=4.37,<5.0a0
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 2032643
  timestamp: 1755254835402
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_2.conda
  sha256: 0348fcaaefba8b77e7f9889a0d8ed356ff8aa53c1b69b47697b5c8c5f7d33b0e
  md5: b6daf7dbe075ac2ae2ad59cdc45aa8c4
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - python_abi 3.12.* *_cp312
  - liblapack >=3.9.0,<4.0a0
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=compressed-mapping
  size: 8785544
  timestamp: 1756343060399
- pypi: https://files.pythonhosted.org/packages/82/df/4b01f10069e23c641f116c62fc31e31e8dc361a153175d81561d15c8143b/nvidia_cublas_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
  name: nvidia-cublas-cu12
  version: *********
  sha256: 3f0e05e7293598cf61933258b73e66a160c27d59c4422670bf0b79348c04be44
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/39/6f/3683ecf4e38931971946777d231c2df00dd5c1c4c2c914c42ad8f9f4dca6/nvidia_cuda_cupti_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-cuda-cupti-cu12
  version: 12.8.57
  sha256: 8e0b2eb847de260739bee4a3f66fac31378f4ff49538ff527a38a01a9a39f950
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/d4/22/32029d4583f7b19cfe75c84399cbcfd23f2aaf41c66fc8db4da460104fff/nvidia_cuda_nvrtc_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
  name: nvidia-cuda-nvrtc-cu12
  version: 12.8.61
  sha256: a0fa9c2a21583105550ebd871bd76e2037205d56f33f128e69f6d2a55e0af9ed
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/16/f6/0e1ef31f4753a44084310ba1a7f0abaf977ccd810a604035abb43421c057/nvidia_cuda_runtime_cu12-12.8.57-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-cuda-runtime-cu12
  version: 12.8.57
  sha256: 75342e28567340b7428ce79a5d6bb6ca5ff9d07b69e7ce00d2c7b4dc23eff0be
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/25/dc/dc825c4b1c83b538e207e34f48f86063c88deaa35d46c651c7c181364ba2/nvidia_cudnn_cu12-********-py3-none-manylinux_2_27_x86_64.whl
  name: nvidia-cudnn-cu12
  version: ********
  sha256: 6d011159a158f3cfc47bf851aea79e31bcff60d530b70ef70474c84cac484d07
  requires_dist:
  - nvidia-cublas-cu12
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/ac/26/b53c493c38dccb1f1a42e1a21dc12cba2a77fbe36c652f7726d9ec4aba28/nvidia_cufft_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-cufft-cu12
  version: *********
  sha256: da650080ab79fcdf7a4b06aa1b460e99860646b176a43f6208099bdc17836b6a
  requires_dist:
  - nvidia-nvjitlink-cu12
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/e5/9c/1f3264d0a84c8a031487fb7f59780fc78fa6f1c97776233956780e3dc3ac/nvidia_cufile_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-cufile-cu12
  version: *********
  sha256: 483f434c541806936b98366f6d33caef5440572de8ddf38d453213729da3e7d4
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/bd/fc/7be5d0082507269bb04ac07cc614c84b78749efb96e8cf4100a8a1178e98/nvidia_curand_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
  name: nvidia-curand-cu12
  version: *********
  sha256: 8387d974240c91f6a60b761b83d4b2f9b938b7e0b9617bae0f0dafe4f5c36b86
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/c2/08/953675873a136d96bb12f93b49ba045d1107bc94d2551c52b12fa6c7dec3/nvidia_cusolver_cu12-*********-py3-none-manylinux_2_27_x86_64.whl
  name: nvidia-cusolver-cu12
  version: *********
  sha256: 4d1354102f1e922cee9db51920dba9e2559877cf6ff5ad03a00d853adafb191b
  requires_dist:
  - nvidia-cublas-cu12
  - nvidia-nvjitlink-cu12
  - nvidia-cusparse-cu12
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/c2/ab/31e8149c66213b846c082a3b41b1365b831f41191f9f40c6ddbc8a7d550e/nvidia_cusparse_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-cusparse-cu12
  version: *********
  sha256: 3c1b61eb8c85257ea07e9354606b26397612627fdcd327bfd91ccf6155e7c86d
  requires_dist:
  - nvidia-nvjitlink-cu12
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/3b/9a/72ef35b399b0e183bc2e8f6f558036922d453c4d8237dab26c666a04244b/nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl
  name: nvidia-cusparselt-cu12
  version: 0.6.3
  sha256: e5c8a26c36445dd2e6812f1177978a24e2d37cacce7e090f297a688d1ec44f46
- pypi: https://files.pythonhosted.org/packages/67/ca/f42388aed0fddd64ade7493dbba36e1f534d4e6fdbdd355c6a90030ae028/nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-nccl-cu12
  version: 2.26.2
  sha256: 694cf3879a206553cc9d7dbda76b13efaf610fdb70a50cba303de1b0d1530ac6
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/03/f8/9d85593582bd99b8d7c65634d2304780aefade049b2b94d96e44084be90b/nvidia_nvjitlink_cu12-12.8.61-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl
  name: nvidia-nvjitlink-cu12
  version: 12.8.61
  sha256: 45fd79f2ae20bd67e8bc411055939049873bfd8fac70ff13bd4865e0b9bdab17
  requires_python: '>=3'
- pypi: https://files.pythonhosted.org/packages/8d/cd/0e8c51b2ae3a58f054f2e7fe91b82d201abfb30167f2431e9bd92d532f42/nvidia_nvtx_cu12-12.8.55-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  name: nvidia-nvtx-cu12
  version: 12.8.55
  sha256: 2dd0780f1a55c21d8e06a743de5bd95653de630decfff40621dbde78cc307102
  requires_python: '>=3'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
  sha256: 2254dae821b286fb57c61895f2b40e3571a070910fdab79a948ff703e1ea807b
  md5: 56f8947aa9d5cf37b0b3d43b83f34192
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - opencl-headers >=2024.10.24
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 106742
  timestamp: 1743700382939
- conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
  sha256: 2b6ce54174ec19110e1b3c37455f7cd138d0e228a75727a9bba443427da30a36
  md5: 45c3d2c224002d6d0d7769142b29f986
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 55357
  timestamp: 1749853464518
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
  sha256: c9f54d4e8212f313be7b02eb962d0cb13a8dae015683a403d3accd4add3e520e
  md5: ffffb341206dd0dab0c36053c048d621
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=14
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3128847
  timestamp: 1754465526100
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.46-h1321c63_0.conda
  sha256: 5c7380c8fd3ad5fc0f8039069a45586aa452cf165264bc5a437ad80397b32934
  md5: 7fa07cb0fb1b625a089ccc01218ee5b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1209177
  timestamp: 1756742976157
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.0-hab00c5b_0_cpython.conda
  sha256: 5398ebae6a1ccbfd3f76361eac75f3ac071527a8072627c4bf9008c689034f48
  md5: 7f97faab5bebcc2580f4f299285323da
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.5.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libnsl >=2.0.0,<2.1.0a0
  - libsqlite >=3.43.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - ncurses >=6.4,<7.0a0
  - openssl >=3.1.3,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - xz >=5.2.6,<6.0a0
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  purls: []
  size: 32123473
  timestamp: 1696324522323
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
  sha256: b8afeaefe409d61fa4b68513b25a66bb17f3ca430d67cfea51083c7bfbe098ef
  md5: 859c6bec94cd74119f12b961aba965a8
  depends:
  - cpython 3.12.11.*
  - python_abi * *_cp312
  license: Python-2.0
  purls: []
  size: 45836
  timestamp: 1749047798827
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
  build_number: 8
  sha256: 80677180dd3c22deb7426ca89d6203f1c7f1f256f2d5a94dc210f6e758229809
  md5: c3efd25ac4d74b1584d2f7a57195ddf1
  constrains:
  - python 3.12.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6958
  timestamp: 1752805918820
- pypi: https://download.pytorch.org/whl/pytorch_triton_rocm-3.3.1-cp312-cp312-linux_x86_64.whl
  name: pytorch-triton-rocm
  version: 3.3.1
  sha256: 977423eee5c542a3f8aa4f527aec1688c4d485f207089cb595a8e638fcc3888a
  requires_dist:
  - setuptools>=40.8.0
  - cmake>=3.20 ; extra == 'build'
  - lit ; extra == 'build'
  - autopep8 ; extra == 'tests'
  - isort ; extra == 'tests'
  - numpy ; extra == 'tests'
  - pytest ; extra == 'tests'
  - pytest-forked ; extra == 'tests'
  - pytest-xdist ; extra == 'tests'
  - scipy>=1.7.1 ; extra == 'tests'
  - llnl-hatchet ; extra == 'tests'
  - matplotlib ; extra == 'tutorials'
  - pandas ; extra == 'tutorials'
  - tabulate ; extra == 'tutorials'
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- pypi: https://files.pythonhosted.org/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl
  name: setuptools
  version: 80.9.0
  sha256: 062d34222ad13e0cc312a4c02d73f059e86a4acbfbdea8f8f76b28c99f306922
  requires_dist:
  - pytest>=6,!=8.1.* ; extra == 'test'
  - virtualenv>=13.0.0 ; extra == 'test'
  - wheel>=0.44.0 ; extra == 'test'
  - pip>=19.1 ; extra == 'test'
  - packaging>=24.2 ; extra == 'test'
  - jaraco-envs>=2.2 ; extra == 'test'
  - pytest-xdist>=3 ; extra == 'test'
  - jaraco-path>=3.7.2 ; extra == 'test'
  - build[virtualenv]>=1.0.3 ; extra == 'test'
  - filelock>=3.4.0 ; extra == 'test'
  - ini2toml[lite]>=0.14 ; extra == 'test'
  - tomli-w>=1.0.0 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-perf ; sys_platform != 'cygwin' and extra == 'test'
  - jaraco-develop>=7.21 ; python_full_version >= '3.9' and sys_platform != 'cygwin' and extra == 'test'
  - pytest-home>=0.5 ; extra == 'test'
  - pytest-subprocess ; extra == 'test'
  - pyproject-hooks!=1.1 ; extra == 'test'
  - jaraco-test>=5.5 ; extra == 'test'
  - sphinx>=3.5 ; extra == 'doc'
  - jaraco-packaging>=9.3 ; extra == 'doc'
  - rst-linker>=1.9 ; extra == 'doc'
  - furo ; extra == 'doc'
  - sphinx-lint ; extra == 'doc'
  - jaraco-tidelift>=1.4 ; extra == 'doc'
  - pygments-github-lexers==0.0.5 ; extra == 'doc'
  - sphinx-favicon ; extra == 'doc'
  - sphinx-inline-tabs ; extra == 'doc'
  - sphinx-reredirects ; extra == 'doc'
  - sphinxcontrib-towncrier ; extra == 'doc'
  - sphinx-notfound-page>=1,<2 ; extra == 'doc'
  - pyproject-hooks!=1.1 ; extra == 'doc'
  - towncrier<24.7 ; extra == 'doc'
  - packaging>=24.2 ; extra == 'core'
  - more-itertools>=8.8 ; extra == 'core'
  - jaraco-text>=3.7 ; extra == 'core'
  - importlib-metadata>=6 ; python_full_version < '3.10' and extra == 'core'
  - tomli>=2.0.1 ; python_full_version < '3.11' and extra == 'core'
  - wheel>=0.43.0 ; extra == 'core'
  - platformdirs>=4.2.2 ; extra == 'core'
  - jaraco-functools>=4 ; extra == 'core'
  - more-itertools ; extra == 'core'
  - pytest-checkdocs>=2.4 ; extra == 'check'
  - pytest-ruff>=0.2.1 ; sys_platform != 'cygwin' and extra == 'check'
  - ruff>=0.8.0 ; sys_platform != 'cygwin' and extra == 'check'
  - pytest-cov ; extra == 'cover'
  - pytest-enabler>=2.2 ; extra == 'enabler'
  - pytest-mypy ; extra == 'type'
  - mypy==1.14.* ; extra == 'type'
  - importlib-metadata>=7.0.2 ; python_full_version < '3.10' and extra == 'type'
  - jaraco-develop>=7.21 ; sys_platform != 'cygwin' and extra == 'type'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl
  name: sympy
  version: 1.14.0
  sha256: e091cc3e99d2141a0ba2847328f5479b05d94a6635cb96148ccb3f34671bd8f5
  requires_dist:
  - mpmath>=1.1.0,<1.4
  - pytest>=7.1.0 ; extra == 'dev'
  - hypothesis>=6.70.0 ; extra == 'dev'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
  sha256: 0053c17ffbd9f8af1a7f864995d70121c292e317804120be4667f37c92805426
  md5: 1bad93f0aa428d618875ef3a588a889e
  depends:
  - __glibc >=2.28
  - kernel-headers_linux-64 4.18.0 he073ed8_8
  - tzdata
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 24210909
  timestamp: 1752669140965
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3285204
  timestamp: 1748387766691
- pypi: https://download.pytorch.org/whl/cu128/torch-2.7.1%2Bcu128-cp312-cp312-manylinux_2_28_x86_64.whl
  name: torch
  version: 2.7.1+cu128
  sha256: 0b64f7d0a6f2a739ed052ba959f7b67c677028c9566ce51997f9f90fe573ddaa
  requires_dist:
  - filelock
  - typing-extensions>=4.10.0
  - setuptools ; python_full_version >= '3.12'
  - sympy>=1.13.3
  - networkx
  - jinja2
  - fsspec
  - nvidia-cuda-nvrtc-cu12==12.8.61 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cuda-runtime-cu12==12.8.57 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cuda-cupti-cu12==12.8.57 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cudnn-cu12==******** ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cublas-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cufft-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-curand-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cusolver-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cusparse-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cusparselt-cu12==0.6.3 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-nccl-cu12==2.26.2 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-nvtx-cu12==12.8.55 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-nvjitlink-cu12==12.8.61 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - nvidia-cufile-cu12==********* ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - triton==3.3.1 ; sys_platform == 'linux'
  - optree>=0.13.0 ; extra == 'optree'
  - opt-einsum>=3.3 ; extra == 'opt-einsum'
  requires_python: '>=3.9.0'
- pypi: https://download.pytorch.org/whl/rocm6.3/torch-2.7.1%2Brocm6.3-cp312-cp312-manylinux_2_28_x86_64.whl
  name: torch
  version: 2.7.1+rocm6.3
  sha256: b0c10342f64a34998ae8d5084aa1beae7e11defa46a4e05fe9aa6f09ffb0db37
  requires_dist:
  - filelock
  - typing-extensions>=4.10.0
  - setuptools ; python_full_version >= '3.12'
  - sympy>=1.13.3
  - networkx
  - jinja2
  - fsspec
  - pytorch-triton-rocm==3.3.1 ; platform_machine == 'x86_64' and sys_platform == 'linux'
  - optree>=0.13.0 ; extra == 'optree'
  - opt-einsum>=3.3 ; extra == 'opt-einsum'
  requires_python: '>=3.9.0'
- pypi: https://files.pythonhosted.org/packages/24/5f/950fb373bf9c01ad4eb5a8cd5eaf32cdf9e238c02f9293557a2129b9c4ac/triton-3.3.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
  name: triton
  version: 3.3.1
  sha256: 9999e83aba21e1a78c1f36f21bce621b77bcaa530277a50484a7cb4a822f6e43
  requires_dist:
  - setuptools>=40.8.0
  - cmake>=3.20 ; extra == 'build'
  - lit ; extra == 'build'
  - autopep8 ; extra == 'tests'
  - isort ; extra == 'tests'
  - numpy ; extra == 'tests'
  - pytest ; extra == 'tests'
  - pytest-forked ; extra == 'tests'
  - pytest-xdist ; extra == 'tests'
  - scipy>=1.7.1 ; extra == 'tests'
  - llnl-hatchet ; extra == 'tests'
  - matplotlib ; extra == 'tutorials'
  - pandas ; extra == 'tutorials'
  - tabulate ; extra == 'tutorials'
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
  sha256: 7c2df5721c742c2a47b2c8f960e718c930031663ac1174da67c1ed5999f7938c
  md5: edd329d7d3a4ab45dcf905899a7a6115
  depends:
  - typing_extensions ==4.15.0 pyhcf101f3_0
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 91383
  timestamp: 1756220668932
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
  sha256: 032271135bca55aeb156cee361c81350c6f3fb203f57d024d7e5a1fc9ef18731
  md5: 0caa1af407ecff61170c9437a808404d
  depends:
  - python >=3.10
  - python
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/typing-extensions?source=compressed-mapping
  size: 51692
  timestamp: 1756220668932
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.8.1-hbcc6ac9_2.conda
  sha256: 802725371682ea06053971db5b4fb7fbbcaee9cb1804ec688f55e51d74660617
  md5: 68eae977d7d1196d32b636a026dc015d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - liblzma 5.8.1 hb9d3cd8_2
  - liblzma-devel 5.8.1 hb9d3cd8_2
  - xz-gpl-tools 5.8.1 hbcc6ac9_2
  - xz-tools 5.8.1 hb9d3cd8_2
  license: 0BSD AND LGPL-2.1-or-later AND GPL-2.0-or-later
  purls: []
  size: 23987
  timestamp: 1749230104359
- conda: https://conda.anaconda.org/conda-forge/linux-64/xz-gpl-tools-5.8.1-hbcc6ac9_2.conda
  sha256: 840838dca829ec53f1160f3fca6dbfc43f2388b85f15d3e867e69109b168b87b
  md5: bf627c16aa26231720af037a2709ab09
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - liblzma 5.8.1 hb9d3cd8_2
  constrains:
  - xz 5.8.1.*
  license: 0BSD AND LGPL-2.1-or-later AND GPL-2.0-or-later
  purls: []
  size: 33911
  timestamp: 1749230090353
- conda: https://conda.anaconda.org/conda-forge/linux-64/xz-tools-5.8.1-hb9d3cd8_2.conda
  sha256: 58034f3fca491075c14e61568ad8b25de00cb3ae479de3e69be6d7ee5d3ace28
  md5: 1bad2995c8f1c8075c6c331bf96e46fb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - liblzma 5.8.1 hb9d3cd8_2
  constrains:
  - xz 5.8.1.*
  license: 0BSD AND LGPL-2.1-or-later
  purls: []
  size: 96433
  timestamp: 1749230076687
